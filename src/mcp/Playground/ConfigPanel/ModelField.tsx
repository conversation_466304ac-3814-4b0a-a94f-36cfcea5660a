import {Flex, Form, Select, Typography} from 'antd';
import {useRequest} from 'huse';
import {Tag} from '@panda-design/components';
import {useCallback, useEffect} from 'react';
import {apiGetManyModel} from '@/api/mcp/playground';

const getTagColor = (tags: string) => {
    if (tags === '图像理解') {
        return 'success';
    }
    return 'info';
};

interface ModelSelectProps {
    data: any[];
    loading?: boolean;
    value?: {id: number, name: string};
    onChange?: (value?: {id: number, name: string}) => void;
}

const ModelSelect = ({value, onChange, data, loading}: ModelSelectProps) => {

    const handleChange = useCallback(
        (value: number, option: any) => onChange && onChange({id: value, name: option.name}),
        [onChange]
    );

    return (
        <Select
            loading={loading}
            options={data || []}
            value={value?.id}
            onChange={handleChange}
            fieldNames={{label: 'name', value: 'id'}}
            labelRender={({label, value}) => {
                const option = data?.find(d => d.id === value);
                return (
                    <Flex align="center" gap={8}>
                        <Typography.Text ellipsis>
                            {label}
                        </Typography.Text>
                        {option?.tags && (
                            <Tag type="flat" round color={getTagColor(option.tags)}>{option.tags}</Tag>
                        )}
                    </Flex>
                );
            }}
            optionRender={option => (
                <Flex align="center" justify="space-between" gap={8}>
                    <Typography.Text ellipsis>
                        {option.label}
                    </Typography.Text>
                    {option.data.tags && (
                        <Tag type="flat" round color={getTagColor(option.data.tags)}>{option.data.tags}</Tag>
                    )}
                </Flex>
            )}
        />
    );
};
export default function ModelField() {
    const modelId = Form.useWatch(['model', 'id']);
    const form = Form.useFormInstance();
    const {data, pending} = useRequest(apiGetManyModel, undefined);

    useEffect(
        () => {
            if (!modelId && data && data.length > 0) {
                form.setFieldsValue({
                    model: {id: data[0].id, name: data[0].name},
                });
            }
        },
        [data, form, modelId]
    );

    return (
        <>
            <Form.Item required label="模型" name="model">
                <ModelSelect data={data || []} loading={pending} />
            </Form.Item>
        </>
    );
}
